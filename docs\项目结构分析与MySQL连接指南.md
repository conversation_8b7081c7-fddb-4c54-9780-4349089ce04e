# 智慧校园系统 - 项目结构分析与MySQL连接指南

## 📋 目录
1. [项目整体架构](#项目整体架构)
2. [详细目录结构](#详细目录结构)
3. [MySQL数据库连接配置](#mysql数据库连接配置)
4. [快速启动指南](#快速启动指南)
5. [核心功能模块](#核心功能模块)

---

## 🏗️ 项目整体架构

### 系统架构图
```
智慧校园系统
├── 前端 (Web界面)
│   ├── index.html (主页面)
│   ├── js/ (JavaScript模块)
│   ├── geojson2_data/ (地图数据)
│   └── map_data2/ (Shapefile地图数据)
│
├── 后端 (Node.js服务)
│   ├── server.js (Express服务器)
│   ├── package.json (依赖配置)
│   └── node_modules/ (依赖包)
│
└── 数据库 (MySQL)
    └── campus_map (数据库名)
        ├── buildings (建筑物表)
        ├── user_sessions (用户会话表)
        └── user_actions (用户行为表)
```

### 技术栈
- **前端**: HTML5 + CSS3 + JavaScript (ES6+) + OpenLayers地图
- **后端**: Node.js + Express.js + MySQL2
- **数据库**: MySQL 8.0+
- **地图数据**: GeoJSON + Shapefile格式

---

## 📁 详细目录结构

```
项目根目录/
├── 📄 index.html                    # 主页面入口
├── 📄 script.js                     # 主要业务逻辑脚本
├── 📄 i18n.js                       # 国际化配置
├── 🖼️ icon.ico                      # 网站图标
├── 🖼️ 南通大学校徽.ico               # 校徽图标
├── 📄 个人理解.txt                   # 开发笔记
│
├── 📁 js/                           # JavaScript模块目录
│   ├── 📄 api.js                    # API接口模块
│   ├── 📄 auth.js                   # 用户认证模块
│   ├── 📄 building-classifier.js    # 建筑物分类器
│   ├── 📄 compass.js                # 指南针功能
│   ├── 📄 config.js                 # 配置管理
│   ├── 📄 distance-measurement.js   # 距离测量
│   ├── 📄 error-handler.js          # 错误处理
│   ├── 📄 initialization.js         # 初始化模块
│   ├── 📄 layer-control.js          # 图层控制
│   ├── 📄 layer-manager.js          # 图层管理器
│   ├── 📄 loading.js                # 加载状态管理
│   ├── 📄 main.js                   # 主控制器
│   ├── 📄 map-core.js               # 地图核心功能
│   ├── 📄 panel-manager.js          # 面板管理器
│   ├── 📄 route-planning.js         # 路径规划
│   ├── 📄 search.js                 # 搜索功能
│   ├── 📄 system-info.js            # 系统信息
│   ├── 📄 user-tracking.js          # 用户行为追踪
│   └── 📄 weather.js                # 天气模块
│
├── 📁 geojson2_data/                # GeoJSON地图数据
│   ├── 📄 boundary.geojson          # 校园边界
│   ├── 📄 buildings.geojson         # 建筑物数据
│   ├── 📄 rivers.geojson            # 河流数据
│   ├── 📄 roads.geojson             # 道路数据
│   ├── 📄 traffic.geojson           # 交通设施
│   └── 📄 waters.geojson            # 水域数据
│
├── 📁 map_data2/                    # Shapefile地图数据
│   ├── 📄 boundary.*                # 边界Shapefile文件集
│   ├── 📄 buildings.*               # 建筑物Shapefile文件集
│   ├── 📄 rivers.*                  # 河流Shapefile文件集
│   ├── 📄 roads.*                   # 道路Shapefile文件集
│   └── 📄 waters.*                  # 水域Shapefile文件集
│
├── 📁 智慧校园后端服务器/            # 后端服务目录
│   ├── 📄 server.js                 # Express服务器主文件
│   ├── 📄 package.json              # Node.js项目配置
│   ├── 📄 package-lock.json         # 依赖版本锁定
│   ├── 📄 clear_user_actions.js     # 用户行为清理脚本
│   └── 📁 node_modules/             # Node.js依赖包
│
└── 📁 md文档/                       # 项目文档
    └── 📄 数据库.md                 # 数据库详细说明文档
```

---

## 🗄️ MySQL数据库连接配置

### 1. 数据库环境要求
- **MySQL版本**: 8.0+ (推荐)
- **字符集**: utf8mb4
- **端口**: 3306 (默认)

### 2. 数据库创建脚本
```sql
-- 创建数据库
CREATE DATABASE campus_map CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE campus_map;

-- 创建建筑物表
CREATE TABLE buildings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL COMMENT '建筑物名称',
    type VARCHAR(50) NOT NULL COMMENT '建筑物类型',
    description TEXT COMMENT '建筑物描述',
    longitude DECIMAL(10, 7) NOT NULL COMMENT '经度',
    latitude DECIMAL(10, 7) NOT NULL COMMENT '纬度',
    floor_count INT DEFAULT 1 COMMENT '楼层数',
    area DECIMAL(10, 2) COMMENT '建筑面积(平方米)',
    build_year YEAR COMMENT '建造年份',
    status ENUM('active', 'maintenance', 'closed') DEFAULT 'active' COMMENT '状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) COMMENT='建筑物信息表';

-- 插入示例数据
INSERT INTO buildings (name, type, description, longitude, latitude, floor_count, area, build_year) VALUES
('图书馆', '公共建筑', '南通大学主图书馆，学习研究中心', 120.904569, 31.975849, 12, 14844.00, 2010),
('第一教学楼', '教学建筑', '主要的教学楼', 120.907800, 31.975200, 5, 8000.00, 2008),
('学生食堂', '食堂', '学生用餐场所', 120.909200, 31.974800, 3, 3000.00, 2009),
('体育馆', '体育建筑', '综合体育活动中心', 120.906500, 31.976500, 2, 5000.00, 2012),
('实验楼', '实验建筑', '理工科实验室', 120.908000, 31.974000, 6, 7500.00, 2015);
```

### 3. 后端连接配置
编辑 `智慧校园后端服务器/server.js` 文件中的数据库配置：

```javascript
// 数据库配置 (第15-22行)
const dbConfig = {
    host: 'localhost',          // 数据库主机地址
    port: 3306,                 // 数据库端口
    user: 'root',               // 数据库用户名
    password: '你的MySQL密码',   // ⚠️ 修改为你的实际密码
    database: 'campus_map',     // 数据库名称
    charset: 'utf8mb4'          // 字符集
};
```

### 4. 连接测试方法
```bash
# 1. 进入后端目录
cd 智慧校园后端服务器

# 2. 安装依赖
npm install

# 3. 启动服务器
npm start
```

**成功连接标志**:
```
✅ 数据库连接成功
🚀 服务器启动成功!
📍 地址: http://localhost:3001
🗄️ 数据库: campus_map
```

---

## 🚀 快速启动指南

### 步骤1: 环境准备
1. 安装 MySQL 8.0+
2. 安装 Node.js 16.0+
3. 创建数据库并导入数据

### 步骤2: 后端启动
```bash
cd 智慧校园后端服务器
npm install
npm start
```

### 步骤3: 前端访问
1. 使用浏览器打开 `index.html`
2. 或使用本地服务器 (推荐):
```bash
# 使用Python
python -m http.server 8080

# 使用Node.js
npx http-server -p 8080
```

### 步骤4: 功能验证
- 搜索"图书馆"测试搜索功能
- 点击地图建筑物测试信息显示
- 检查浏览器控制台无错误信息

---

## 🧩 核心功能模块

### 前端模块架构
```
主控制器 (main.js)
├── 地图核心 (map-core.js)
├── API接口 (api.js)
├── 搜索功能 (search.js)
├── 图层管理 (layer-manager.js)
├── 面板管理 (panel-manager.js)
├── 路径规划 (route-planning.js)
├── 用户追踪 (user-tracking.js)
└── 错误处理 (error-handler.js)
```

### 后端API接口
| 接口路径 | 方法 | 功能 | 状态 |
|----------|------|------|------|
| `/api/buildings` | GET | 获取建筑物列表/搜索 | ✅ 使用中 |
| `/api/buildings/:id` | GET | 获取建筑物详情 | ✅ 使用中 |
| `/api/weather` | GET | 获取天气信息 | ⚠️ 静态数据 |
| `/api/health` | GET | 健康检查 | ✅ 使用中 |
| `/api/user/action` | POST | 记录用户行为 | ✅ 使用中 |

### 数据库表结构
- **buildings**: 建筑物基础信息
- **user_sessions**: 用户会话记录
- **user_actions**: 用户行为追踪

---

## 🔧 常见问题解决

### 数据库连接失败
1. 检查MySQL服务状态
2. 验证用户名密码
3. 确认数据库名称正确
4. 检查防火墙设置

### 前端无法获取数据
1. 确认后端服务启动
2. 检查CORS配置
3. 验证API接口地址
4. 查看浏览器控制台错误

### 地图显示异常
1. 检查地图数据文件完整性
2. 验证GeoJSON格式
3. 确认网络连接正常
4. 检查OpenLayers版本兼容性

---

## 🔐 MySQL连接详细配置指南

### 主机MySQL数据库连接方案

#### 方案1: 本地MySQL连接 (推荐)
```javascript
// server.js 配置
const dbConfig = {
    host: 'localhost',          // 本地主机
    port: 3306,                 // 默认端口
    user: 'root',               // 管理员用户
    password: '你的密码',        // 实际密码
    database: 'campus_map',     // 数据库名
    charset: 'utf8mb4',
    // 连接池配置
    waitForConnections: true,
    connectionLimit: 10,        // 最大连接数
    queueLimit: 0,             // 队列限制
    acquireTimeout: 60000,     // 获取连接超时
    timeout: 60000,            // 查询超时
    reconnect: true,           // 自动重连
    idleTimeout: 300000        // 空闲超时
};
```

#### 方案2: 远程MySQL连接
```javascript
// 连接远程MySQL服务器
const dbConfig = {
    host: '*************',     // 远程主机IP
    port: 3306,
    user: 'campus_user',       // 专用用户
    password: 'secure_password',
    database: 'campus_map',
    charset: 'utf8mb4',
    ssl: {                     // SSL连接配置
        rejectUnauthorized: false
    }
};
```

#### 方案3: 云数据库连接 (阿里云/腾讯云)
```javascript
// 云数据库连接示例
const dbConfig = {
    host: 'rm-xxxxxxxx.mysql.rds.aliyuncs.com',
    port: 3306,
    user: 'campus_admin',
    password: 'CloudPassword123!',
    database: 'campus_map',
    charset: 'utf8mb4',
    ssl: {
        ca: fs.readFileSync('path/to/ca-cert.pem'),
        cert: fs.readFileSync('path/to/client-cert.pem'),
        key: fs.readFileSync('path/to/client-key.pem')
    }
};
```

### 环境变量配置 (安全推荐)
创建 `.env` 文件：
```env
# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=你的密码
DB_NAME=campus_map
DB_CHARSET=utf8mb4

# 服务器配置
SERVER_PORT=3001
NODE_ENV=development
```

修改 `server.js` 使用环境变量：
```javascript
require('dotenv').config();

const dbConfig = {
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 3306,
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD,
    database: process.env.DB_NAME || 'campus_map',
    charset: process.env.DB_CHARSET || 'utf8mb4'
};
```

---

## 🛠️ MySQL用户权限配置

### 创建专用数据库用户
```sql
-- 创建专用用户
CREATE USER 'campus_user'@'localhost' IDENTIFIED BY 'secure_password';

-- 授予数据库权限
GRANT SELECT, INSERT, UPDATE, DELETE ON campus_map.* TO 'campus_user'@'localhost';

-- 刷新权限
FLUSH PRIVILEGES;

-- 验证用户权限
SHOW GRANTS FOR 'campus_user'@'localhost';
```

### 远程连接权限配置
```sql
-- 允许远程连接
CREATE USER 'campus_user'@'%' IDENTIFIED BY 'secure_password';
GRANT SELECT, INSERT, UPDATE, DELETE ON campus_map.* TO 'campus_user'@'%';
FLUSH PRIVILEGES;
```

---

## 🚨 故障排除完整指南

### 1. 连接错误诊断

#### 错误: `connect ECONNREFUSED`
```bash
❌ Error: connect ECONNREFUSED 127.0.0.1:3306
```
**解决步骤**:
1. 检查MySQL服务状态
```bash
# Windows
net start mysql

# Linux/Mac
sudo systemctl start mysql
# 或
brew services start mysql
```

2. 验证端口监听
```bash
netstat -an | grep 3306
```

3. 检查防火墙设置
```bash
# Windows防火墙
netsh advfirewall firewall add rule name="MySQL" dir=in action=allow protocol=TCP localport=3306

# Linux防火墙
sudo ufw allow 3306
```

#### 错误: `Access denied for user`
```bash
❌ Error: Access denied for user 'root'@'localhost'
```
**解决步骤**:
1. 重置MySQL密码
```bash
# 停止MySQL服务
sudo systemctl stop mysql

# 安全模式启动
sudo mysqld_safe --skip-grant-tables &

# 连接并重置密码
mysql -u root
USE mysql;
UPDATE user SET authentication_string=PASSWORD('新密码') WHERE User='root';
FLUSH PRIVILEGES;
```

2. 验证用户权限
```sql
SELECT user, host, authentication_string FROM mysql.user WHERE user='root';
```

#### 错误: `Unknown database 'campus_map'`
```bash
❌ Error: Unknown database 'campus_map'
```
**解决步骤**:
1. 创建数据库
```sql
CREATE DATABASE campus_map CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

2. 验证数据库存在
```sql
SHOW DATABASES;
```

### 2. 性能优化配置

#### MySQL配置优化 (my.cnf)
```ini
[mysqld]
# 基础配置
port = 3306
socket = /tmp/mysql.sock
default-storage-engine = InnoDB

# 字符集配置
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci

# 连接配置
max_connections = 200
max_connect_errors = 10
wait_timeout = 28800
interactive_timeout = 28800

# 缓存配置
innodb_buffer_pool_size = 128M
query_cache_size = 32M
query_cache_type = 1

# 日志配置
log-error = /var/log/mysql/error.log
slow_query_log = 1
slow_query_log_file = /var/log/mysql/slow.log
long_query_time = 2
```

#### 连接池优化
```javascript
const pool = mysql.createPool({
    ...dbConfig,
    waitForConnections: true,
    connectionLimit: 10,           // 根据并发需求调整
    queueLimit: 0,
    acquireTimeout: 60000,         // 获取连接超时
    timeout: 60000,                // 查询超时
    reconnect: true,               // 自动重连
    idleTimeout: 300000,           // 空闲连接超时
    // 连接健康检查
    enableKeepAlive: true,
    keepAliveInitialDelay: 0
});
```

### 3. 监控和日志

#### 连接状态监控
```javascript
// 添加到 server.js
setInterval(async () => {
    try {
        const [rows] = await pool.execute('SELECT 1');
        console.log('✅ 数据库连接正常');
    } catch (error) {
        console.error('❌ 数据库连接异常:', error.message);
    }
}, 30000); // 每30秒检查一次
```

#### 详细错误日志
```javascript
// 增强错误处理
pool.on('connection', (connection) => {
    console.log('🔗 新建数据库连接:', connection.threadId);
});

pool.on('error', (err) => {
    console.error('💥 数据库连接池错误:', err);
    if (err.code === 'PROTOCOL_CONNECTION_LOST') {
        console.log('🔄 尝试重新连接...');
    }
});
```

---

## 📊 数据库性能监控

### 关键指标查询
```sql
-- 查看连接状态
SHOW STATUS LIKE 'Threads_connected';
SHOW STATUS LIKE 'Max_used_connections';

-- 查看查询性能
SHOW STATUS LIKE 'Slow_queries';
SHOW STATUS LIKE 'Questions';

-- 查看缓存命中率
SHOW STATUS LIKE 'Qcache_hits';
SHOW STATUS LIKE 'Qcache_inserts';

-- 查看表状态
SHOW TABLE STATUS FROM campus_map;
```

### 索引优化建议
```sql
-- 为常用查询字段添加索引
ALTER TABLE buildings ADD INDEX idx_name (name);
ALTER TABLE buildings ADD INDEX idx_type (type);
ALTER TABLE buildings ADD INDEX idx_status (status);

-- 复合索引
ALTER TABLE buildings ADD INDEX idx_type_status (type, status);

-- 查看索引使用情况
EXPLAIN SELECT * FROM buildings WHERE name LIKE '%图书馆%';
```

---

**📞 技术支持**: 如遇问题请提供具体错误信息和操作步骤，便于快速定位解决！

**🔗 相关文档**:
- [MySQL官方文档](https://dev.mysql.com/doc/)
- [Node.js MySQL2文档](https://github.com/sidorares/node-mysql2)
- [Express.js官方指南](https://expressjs.com/)

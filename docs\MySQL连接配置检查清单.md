# MySQL连接配置检查清单

## ✅ 快速检查清单

### 📋 环境准备检查
- [ ] MySQL 8.0+ 已安装并启动
- [ ] Node.js 16.0+ 已安装
- [ ] 项目文件完整下载
- [ ] 网络连接正常

### 🗄️ 数据库配置检查
- [ ] MySQL服务正在运行
- [ ] 数据库 `campus_map` 已创建
- [ ] 建筑物表 `buildings` 已创建并有数据
- [ ] 数据库用户权限配置正确
- [ ] 字符集设置为 `utf8mb4`

### 🔧 后端配置检查
- [ ] `server.js` 中数据库密码已正确配置
- [ ] 依赖包已安装 (`npm install`)
- [ ] 端口3001未被占用
- [ ] CORS配置正确

### 🌐 前端配置检查
- [ ] `index.html` 可正常打开
- [ ] 浏览器控制台无错误
- [ ] API接口地址配置正确
- [ ] 地图数据文件完整

---

## 🚀 一键启动脚本

### Windows批处理脚本 (start.bat)
```batch
@echo off
echo 智慧校园系统启动脚本
echo ========================

echo 1. 检查MySQL服务...
net start mysql
if %errorlevel% neq 0 (
    echo ❌ MySQL服务启动失败，请检查安装
    pause
    exit /b 1
)

echo 2. 进入后端目录...
cd /d "%~dp0智慧校园后端服务器"

echo 3. 安装依赖包...
npm install

echo 4. 启动后端服务...
start "后端服务" cmd /k "npm start"

echo 5. 等待服务启动...
timeout /t 3

echo 6. 打开前端页面...
cd /d "%~dp0"
start "" "index.html"

echo ✅ 系统启动完成！
echo 后端服务: http://localhost:3001
echo 前端页面: 已在浏览器中打开
pause
```

### Linux/Mac启动脚本 (start.sh)
```bash
#!/bin/bash
echo "智慧校园系统启动脚本"
echo "========================"

echo "1. 检查MySQL服务..."
if ! pgrep -x "mysqld" > /dev/null; then
    echo "启动MySQL服务..."
    sudo systemctl start mysql || brew services start mysql
fi

echo "2. 进入后端目录..."
cd "智慧校园后端服务器"

echo "3. 安装依赖包..."
npm install

echo "4. 启动后端服务..."
npm start &
BACKEND_PID=$!

echo "5. 等待服务启动..."
sleep 3

echo "6. 打开前端页面..."
cd ..
if command -v python3 &> /dev/null; then
    echo "使用Python启动本地服务器..."
    python3 -m http.server 8080 &
    SERVER_PID=$!
    echo "前端服务: http://localhost:8080"
elif command -v python &> /dev/null; then
    echo "使用Python2启动本地服务器..."
    python -m SimpleHTTPServer 8080 &
    SERVER_PID=$!
    echo "前端服务: http://localhost:8080"
else
    echo "直接打开HTML文件..."
    open index.html || xdg-open index.html
fi

echo "✅ 系统启动完成！"
echo "后端服务: http://localhost:3001"
echo "按Ctrl+C停止服务"

# 等待用户中断
trap "kill $BACKEND_PID $SERVER_PID 2>/dev/null; exit" INT
wait
```

---

## 🔍 连接测试工具

### 数据库连接测试脚本 (test-db.js)
```javascript
const mysql = require('mysql2/promise');

// 数据库配置
const dbConfig = {
    host: 'localhost',
    port: 3306,
    user: 'root',
    password: '你的密码',  // 修改为实际密码
    database: 'campus_map',
    charset: 'utf8mb4'
};

async function testConnection() {
    console.log('🔍 开始测试数据库连接...\n');
    
    try {
        // 1. 测试基本连接
        console.log('1️⃣ 测试基本连接...');
        const connection = await mysql.createConnection(dbConfig);
        console.log('✅ 数据库连接成功');
        
        // 2. 测试数据库存在
        console.log('\n2️⃣ 测试数据库存在...');
        const [databases] = await connection.execute('SHOW DATABASES');
        const dbExists = databases.some(db => db.Database === 'campus_map');
        if (dbExists) {
            console.log('✅ 数据库 campus_map 存在');
        } else {
            console.log('❌ 数据库 campus_map 不存在');
            return;
        }
        
        // 3. 测试表存在
        console.log('\n3️⃣ 测试数据表存在...');
        const [tables] = await connection.execute('SHOW TABLES');
        const tableExists = tables.some(table => 
            table[`Tables_in_campus_map`] === 'buildings'
        );
        if (tableExists) {
            console.log('✅ 建筑物表 buildings 存在');
        } else {
            console.log('❌ 建筑物表 buildings 不存在');
            return;
        }
        
        // 4. 测试数据查询
        console.log('\n4️⃣ 测试数据查询...');
        const [rows] = await connection.execute('SELECT COUNT(*) as count FROM buildings');
        const count = rows[0].count;
        if (count > 0) {
            console.log(`✅ 建筑物数据存在，共 ${count} 条记录`);
        } else {
            console.log('⚠️ 建筑物表为空，需要导入数据');
        }
        
        // 5. 测试示例查询
        console.log('\n5️⃣ 测试示例查询...');
        const [buildings] = await connection.execute(
            'SELECT name, type FROM buildings LIMIT 3'
        );
        console.log('✅ 示例建筑物数据:');
        buildings.forEach(building => {
            console.log(`   - ${building.name} (${building.type})`);
        });
        
        await connection.end();
        console.log('\n🎉 所有测试通过！数据库配置正确。');
        
    } catch (error) {
        console.error('\n❌ 数据库连接测试失败:');
        console.error('错误信息:', error.message);
        console.error('\n🔧 请检查以下配置:');
        console.error('1. MySQL服务是否启动');
        console.error('2. 用户名密码是否正确');
        console.error('3. 数据库名称是否正确');
        console.error('4. 端口号是否正确');
    }
}

// 运行测试
testConnection();
```

### API接口测试脚本 (test-api.js)
```javascript
const http = require('http');

const API_BASE = 'http://localhost:3001';

async function testAPI() {
    console.log('🔍 开始测试API接口...\n');
    
    // 测试健康检查接口
    console.log('1️⃣ 测试健康检查接口...');
    try {
        const response = await fetch(`${API_BASE}/api/health`);
        const data = await response.json();
        if (data.status === 'ok') {
            console.log('✅ 健康检查接口正常');
        } else {
            console.log('❌ 健康检查接口异常:', data);
        }
    } catch (error) {
        console.log('❌ 健康检查接口连接失败:', error.message);
        return;
    }
    
    // 测试建筑物接口
    console.log('\n2️⃣ 测试建筑物接口...');
    try {
        const response = await fetch(`${API_BASE}/api/buildings`);
        const data = await response.json();
        if (data.success && data.data.length > 0) {
            console.log(`✅ 建筑物接口正常，返回 ${data.count} 条数据`);
            console.log('示例数据:', data.data[0].name);
        } else {
            console.log('❌ 建筑物接口返回空数据');
        }
    } catch (error) {
        console.log('❌ 建筑物接口测试失败:', error.message);
    }
    
    // 测试搜索功能
    console.log('\n3️⃣ 测试搜索功能...');
    try {
        const response = await fetch(`${API_BASE}/api/buildings?search=图书馆`);
        const data = await response.json();
        if (data.success && data.data.length > 0) {
            console.log('✅ 搜索功能正常');
            console.log('搜索结果:', data.data[0].name);
        } else {
            console.log('⚠️ 搜索功能无结果，可能数据库中没有"图书馆"');
        }
    } catch (error) {
        console.log('❌ 搜索功能测试失败:', error.message);
    }
    
    console.log('\n🎉 API接口测试完成！');
}

// 运行测试
testAPI();
```

---

## 📞 故障排除快速指南

### 常见错误及解决方案

#### 1. 数据库连接失败
```
❌ connect ECONNREFUSED 127.0.0.1:3306
```
**快速解决**:
```bash
# Windows
net start mysql

# Linux/Mac
sudo systemctl start mysql
```

#### 2. 密码错误
```
❌ Access denied for user 'root'@'localhost'
```
**快速解决**:
1. 确认密码正确
2. 重置MySQL密码
3. 检查用户权限

#### 3. 端口占用
```
❌ Error: listen EADDRINUSE :::3001
```
**快速解决**:
```bash
# 查找占用进程
netstat -ano | findstr :3001

# 结束进程
taskkill /PID <进程ID> /F
```

#### 4. 依赖安装失败
```
❌ npm install 失败
```
**快速解决**:
```bash
npm cache clean --force
npm install
```

---

## 📋 配置文件模板

### .env 环境变量模板
```env
# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=你的MySQL密码
DB_NAME=campus_map
DB_CHARSET=utf8mb4

# 服务器配置
SERVER_PORT=3001
NODE_ENV=development

# 安全配置
JWT_SECRET=your_jwt_secret_key
SESSION_SECRET=your_session_secret
```

### 数据库初始化SQL模板
```sql
-- 创建数据库
CREATE DATABASE IF NOT EXISTS campus_map 
CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE campus_map;

-- 创建建筑物表
CREATE TABLE IF NOT EXISTS buildings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL COMMENT '建筑物名称',
    type VARCHAR(50) NOT NULL COMMENT '建筑物类型',
    description TEXT COMMENT '建筑物描述',
    longitude DECIMAL(10, 7) NOT NULL COMMENT '经度',
    latitude DECIMAL(10, 7) NOT NULL COMMENT '纬度',
    floor_count INT DEFAULT 1 COMMENT '楼层数',
    area DECIMAL(10, 2) COMMENT '建筑面积',
    build_year YEAR COMMENT '建造年份',
    status ENUM('active', 'maintenance', 'closed') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_name (name),
    INDEX idx_type (type),
    INDEX idx_status (status)
) COMMENT='建筑物信息表';

-- 插入示例数据
INSERT IGNORE INTO buildings (id, name, type, description, longitude, latitude, floor_count, area, build_year) VALUES
(1, '图书馆', '公共建筑', '南通大学主图书馆，学习研究中心', 120.904569, 31.975849, 12, 14844.00, 2010),
(2, '第一教学楼', '教学建筑', '主要的教学楼', 120.907800, 31.975200, 5, 8000.00, 2008),
(3, '学生食堂', '食堂', '学生用餐场所', 120.909200, 31.974800, 3, 3000.00, 2009),
(4, '体育馆', '体育建筑', '综合体育活动中心', 120.906500, 31.976500, 2, 5000.00, 2012),
(5, '实验楼', '实验建筑', '理工科实验室', 120.908000, 31.974000, 6, 7500.00, 2015);
```

---

**✅ 使用此检查清单可以快速诊断和解决99%的连接问题！**
